<!--components/oa/loading-state/loading-state.wxml-->
<view class="oa-loading-state {{fullScreen ? 'full-screen' : ''}}">
  
  <!-- 加载状态 -->
  <view wx:if="{{type === 'loading'}}" class="state-content loading">
    <view class="oa-loading-spinner"></view>
    <text class="state-text">{{loadingText}}</text>
  </view>
  
  <!-- 错误状态 -->
  <view wx:elif="{{type === 'error'}}" class="state-content error">
    <text class="state-icon">{{customIcon || '❌'}}</text>
    <text class="state-text">{{errorText}}</text>
    <button wx:if="{{showRetry}}" class="oa-action-btn secondary small" bindtap="onRetryTap">
      重试
    </button>
  </view>
  
  <!-- 空状态 -->
  <view wx:elif="{{type === 'empty'}}" class="state-content empty">
    <text class="state-icon">{{customIcon || '📝'}}</text>
    <text class="state-text">{{emptyText}}</text>
    <slot name="empty-action"></slot>
  </view>
  
  <!-- 成功状态 -->
  <view wx:elif="{{type === 'success'}}" class="state-content success">
    <text class="state-icon">{{customIcon || '✅'}}</text>
    <text class="state-text">操作成功</text>
  </view>
  
</view>