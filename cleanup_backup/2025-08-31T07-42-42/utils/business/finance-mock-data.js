// utils/finance-mock-data.js - 财务模拟数据服务
class FinanceMockDataService {
  constructor() {
    this.dataGenerationTime = new Date();
  }

  /**
   * 获取所有报表数据
   */
  static getAllReportData(params = {}) {
    const service = new FinanceMockDataService();
    
    return {
      overview: service.generateOverviewData(params),
      categoryStats: service.generateCategoryStats(params),
      trendData: service.generateTrendData(params),
      healthIntegrationStats: service.generateHealthIntegrationData(params),
      comparisonData: service.generateComparisonData(params)
    };
  }

  /**
   * 生成财务概览数据
   */
  generateOverviewData(params = {}) {
    const baseData = this.getBaseFinanceData(params);
    
    return {
      totalIncome: baseData.totalIncome,
      totalExpense: baseData.totalExpense,
      netProfit: baseData.netProfit,
      transactionCount: baseData.transactionCount,
      avgTransactionAmount: baseData.avgTransactionAmount,
      incomeGrowth: baseData.incomeGrowth,
      expenseGrowth: baseData.expenseGrowth,
      profitMargin: baseData.profitMargin,
      generateTime: new Date().toISOString()
    };
  }

  /**
   * 获取基础财务数据
   */
  getBaseFinanceData(params) {
    const { timeRange = 'month', reportType = 'overview' } = params;
    
    // 根据时间范围调整数据规模
    const multipliers = {
      'week': 0.25,
      'month': 1,
      'quarter': 3,
      'year': 12,
      'custom': 1
    };
    
    const multiplier = multipliers[timeRange] || 1;
    
    // 基础收入数据（养鹅业务相关）
    const baseIncome = {
      goslingSales: 15000 * multiplier,      // 鹅苗销售
      meatSales: 25000 * multiplier,         // 鹅肉销售
      eggSales: 8000 * multiplier,           // 鹅蛋销售
      featherSales: 3000 * multiplier,       // 鹅毛销售
      otherIncome: 2000 * multiplier         // 其他收入
    };
    
    const totalIncome = Object.values(baseIncome).reduce((sum, value) => sum + value, 0);
    
    // 基础支出数据（养鹅业务相关）
    const baseExpense = {
      feedCost: 18000 * multiplier,          // 饲料成本
      goslingPurchase: 8000 * multiplier,    // 鹅苗采购
      healthCare: 3500 * multiplier,         // 健康管理
      laborCost: 6000 * multiplier,          // 人工费用
      facilityCost: 2500 * multiplier,       // 设施维护
      utilitiesCost: 1500 * multiplier,      // 水电费
      otherExpense: 1200 * multiplier        // 其他支出
    };
    
    const totalExpense = Object.values(baseExpense).reduce((sum, value) => sum + value, 0);
    
    const netProfit = totalIncome - totalExpense;
    const profitMargin = totalIncome > 0 ? (netProfit / totalIncome * 100).toFixed(2) : '0.00';
    
    // 交易数量（根据时间范围调整）
    const baseTransactionCount = Math.round(25 * multiplier);
    const avgTransactionAmount = baseTransactionCount > 0 ? (totalIncome / baseTransactionCount) : 0;
    
    // 增长率（模拟同期比较）
    const incomeGrowth = this.generateGrowthRate(8, 25);
    const expenseGrowth = this.generateGrowthRate(5, 18);
    
    return {
      totalIncome: Math.round(totalIncome),
      totalExpense: Math.round(totalExpense),
      netProfit: Math.round(netProfit),
      profitMargin: parseFloat(profitMargin),
      transactionCount: baseTransactionCount,
      avgTransactionAmount: Math.round(avgTransactionAmount),
      incomeGrowth,
      expenseGrowth,
      incomeDetails: baseIncome,
      expenseDetails: baseExpense
    };
  }

  /**
   * 生成分类统计数据
   */
  generateCategoryStats(params = {}) {
    const baseData = this.getBaseFinanceData(params);
    const categories = [];
    
    // 收入分类
    Object.entries(baseData.incomeDetails).forEach(([key, value]) => {
      const categoryName = this.getCategoryName(key);
      categories.push({
        id: key,
        category: categoryName,
        type: 'income',
        amount: value,
        percentage: ((value / baseData.totalIncome) * 100).toFixed(1),
        icon: this.getCategoryIcon(key),
        color: this.getCategoryColor(key, 'income'),
        trend: this.generateTrendIndicator()
      });
    });
    
    // 支出分类
    Object.entries(baseData.expenseDetails).forEach(([key, value]) => {
      const categoryName = this.getCategoryName(key);
      categories.push({
        id: key,
        category: categoryName,
        type: 'expense',
        amount: value,
        percentage: ((value / baseData.totalExpense) * 100).toFixed(1),
        icon: this.getCategoryIcon(key),
        color: this.getCategoryColor(key, 'expense'),
        trend: this.generateTrendIndicator()
      });
    });
    
    return categories.sort((a, b) => b.amount - a.amount);
  }

  /**
   * 生成趋势数据
   */
  generateTrendData(params = {}) {
    const { timeRange = 'month' } = params;
    const trendData = [];
    
    // 根据时间范围生成不同的数据点
    let dataPoints, timeFormat, baseIncome, baseExpense;
    
    switch (timeRange) {
      case 'week':
        dataPoints = 7;
        timeFormat = 'day';
        baseIncome = 2000;
        baseExpense = 1400;
        break;
      case 'month':
        dataPoints = 30;
        timeFormat = 'day';
        baseIncome = 1800;
        baseExpense = 1300;
        break;
      case 'quarter':
        dataPoints = 12;
        timeFormat = 'week';
        baseIncome = 14000;
        baseExpense = 10000;
        break;
      case 'year':
        dataPoints = 12;
        timeFormat = 'month';
        baseIncome = 50000;
        baseExpense = 35000;
        break;
      default:
        dataPoints = 30;
        timeFormat = 'day';
        baseIncome = 1800;
        baseExpense = 1300;
    }
    
    const now = new Date();
    
    for (let i = dataPoints - 1; i >= 0; i--) {
      const date = new Date(now);
      
      if (timeFormat === 'day') {
        date.setDate(date.getDate() - i);
      } else if (timeFormat === 'week') {
        date.setDate(date.getDate() - i * 7);
      } else if (timeFormat === 'month') {
        date.setMonth(date.getMonth() - i);
      }
      
      // 生成带有季节性和随机性的数据
      const seasonalFactor = 1 + Math.sin(i * Math.PI / dataPoints) * 0.2;
      const randomFactor = 0.8 + Math.random() * 0.4;
      
      const income = Math.round(baseIncome * seasonalFactor * randomFactor);
      const expense = Math.round(baseExpense * seasonalFactor * randomFactor * 0.9);
      
      let formattedDate;
      if (timeFormat === 'month') {
        formattedDate = `${date.getMonth() + 1}月`;
      } else if (timeFormat === 'week') {
        formattedDate = `第${Math.ceil((now - date) / (1000 * 60 * 60 * 24 * 7)) + 1}周`;
      } else {
        formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
      }
      
      trendData.push({
        date: date.toISOString().split('T')[0],
        formattedDate,
        income,
        expense,
        profit: income - expense,
        profitMargin: income > 0 ? ((income - expense) / income * 100).toFixed(1) : '0.0'
      });
    }
    
    return trendData;
  }

  /**
   * 生成健康财务整合数据
   */
  generateHealthIntegrationData(params = {}) {
    const baseData = this.getBaseFinanceData(params);
    const healthExpense = baseData.expenseDetails.healthCare || 3500;
    
    return {
      totalHealthExpense: healthExpense,
      healthExpenseRatio: ((healthExpense / baseData.totalExpense) * 100).toFixed(1),
      healthCostByType: [
        {
          type: 'vaccination',
          label: '疫苗接种',
          amount: Math.round(healthExpense * 0.45),
          percentage: 45,
          icon: '💉',
          trend: 'stable'
        },
        {
          type: 'treatment',
          label: '疾病治疗',
          amount: Math.round(healthExpense * 0.35),
          percentage: 35,
          icon: '🏥',
          trend: 'down'
        },
        {
          type: 'prevention',
          label: '预防保健',
          amount: Math.round(healthExpense * 0.20),
          percentage: 20,
          icon: '🛡️',
          trend: 'up'
        }
      ],
      healthEfficiencyMetrics: {
        preventionRatio: 65,
        treatmentRatio: 35,
        avgCostPerHead: 2.85,
        healthROI: 8.2,
        mortalityRate: 3.2,
        vaccinationRate: 98.5
      },
      healthTrendAnalysis: {
        monthlyGrowth: this.generateGrowthRate(-5, 15),
        quarterlyGrowth: this.generateGrowthRate(2, 20),
        yearlyGrowth: this.generateGrowthRate(8, 25)
      },
      healthBudgetAnalysis: {
        budgetUsage: 78.5,
        remainingBudget: Math.round(healthExpense * 0.215),
        projectedYearEnd: Math.round(healthExpense * 12 * 1.15),
        budgetStatus: 'on_track'
      }
    };
  }

  /**
   * 生成对比数据
   */
  generateComparisonData(params = {}) {
    const baseData = this.getBaseFinanceData(params);
    
    // 生成对比期数据（通常是上一期）
    const comparisonIncome = Math.round(baseData.totalIncome * (0.85 + Math.random() * 0.3));
    const comparisonExpense = Math.round(baseData.totalExpense * (0.90 + Math.random() * 0.2));
    
    const incomeChange = ((baseData.totalIncome - comparisonIncome) / comparisonIncome * 100).toFixed(1);
    const expenseChange = ((baseData.totalExpense - comparisonExpense) / comparisonExpense * 100).toFixed(1);
    
    return {
      current: {
        income: baseData.totalIncome,
        expense: baseData.totalExpense,
        profit: baseData.netProfit
      },
      previous: {
        income: comparisonIncome,
        expense: comparisonExpense,
        profit: comparisonIncome - comparisonExpense
      },
      changes: {
        incomeChange: parseFloat(incomeChange),
        expenseChange: parseFloat(expenseChange),
        profitChange: parseFloat(((baseData.netProfit - (comparisonIncome - comparisonExpense)) / (comparisonIncome - comparisonExpense) * 100).toFixed(1))
      },
      analysis: {
        incomeStatus: parseFloat(incomeChange) > 0 ? 'growth' : 'decline',
        expenseStatus: parseFloat(expenseChange) > 10 ? 'high_growth' : parseFloat(expenseChange) > 0 ? 'growth' : 'controlled',
        overallStatus: baseData.netProfit > (comparisonIncome - comparisonExpense) ? 'improved' : 'declined'
      }
    };
  }

  /**
   * 获取分类名称
   */
  getCategoryName(key) {
    const nameMapping = {
      // 收入分类
      'goslingSales': '鹅苗销售',
      'meatSales': '鹅肉销售',
      'eggSales': '鹅蛋销售',
      'featherSales': '鹅毛销售',
      'otherIncome': '其他收入',
      
      // 支出分类
      'feedCost': '饲料成本',
      'goslingPurchase': '鹅苗采购',
      'healthCare': '健康管理',
      'laborCost': '人工费用',
      'facilityCost': '设施维护',
      'utilitiesCost': '水电费',
      'otherExpense': '其他支出'
    };
    
    return nameMapping[key] || key;
  }

  /**
   * 获取分类图标
   */
  getCategoryIcon(key) {
    const iconMapping = {
      // 收入分类图标
      'goslingSales': '🐣',
      'meatSales': '🍖',
      'eggSales': '🥚',
      'featherSales': '🪶',
      'otherIncome': '💰',
      
      // 支出分类图标
      'feedCost': '🌾',
      'goslingPurchase': '🐥',
      'healthCare': '💊',
      'laborCost': '👥',
      'facilityCost': '🏭',
      'utilitiesCost': '⚡',
      'otherExpense': '📋'
    };
    
    return iconMapping[key] || '💼';
  }

  /**
   * 获取分类颜色
   */
  getCategoryColor(key, type) {
    const incomeColors = [
      '#4CAF50', '#66BB6A', '#81C784', '#A5D6A7', '#C8E6C9'
    ];
    
    const expenseColors = [
      '#F44336', '#EF5350', '#E57373', '#FFAB91', '#FFCC02'
    ];
    
    const colors = type === 'income' ? incomeColors : expenseColors;
    const hash = key.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * 生成增长率
   */
  generateGrowthRate(min, max) {
    return parseFloat((min + Math.random() * (max - min)).toFixed(1));
  }

  /**
   * 生成趋势指示器
   */
  generateTrendIndicator() {
    const trends = ['up', 'down', 'stable'];
    const weights = [0.4, 0.3, 0.3]; // 上升趋势概率更高
    
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < trends.length; i++) {
      cumulative += weights[i];
      if (random <= cumulative) {
        return trends[i];
      }
    }
    
    return 'stable';
  }

  /**
   * 生成季节性财务数据（考虑养鹅业务的季节性特点）
   */
  generateSeasonalData(month) {
    // 养鹅业务的季节性特点：
    // 春季(3-5月): 孵化季，成本增加
    // 夏季(6-8月): 生长期，饲料成本高
    // 秋季(9-11月): 出栏季，收入增加
    // 冬季(12-2月): 育肥期，准备下一轮
    
    const seasonalFactors = {
      income: {
        1: 0.8, 2: 0.7, 3: 0.9,   // 冬春准备期
        4: 1.0, 5: 1.1, 6: 1.2,  // 春夏生长期
        7: 1.3, 8: 1.4, 9: 1.5,  // 夏秋出栏期
        10: 1.4, 11: 1.2, 12: 1.0 // 秋冬销售期
      },
      expense: {
        1: 0.9, 2: 0.8, 3: 1.1,   // 冬春准备期
        4: 1.2, 5: 1.3, 6: 1.4,  // 春夏孵化期
        7: 1.3, 8: 1.2, 9: 1.1,  // 夏秋成长期
        10: 1.0, 11: 0.9, 12: 0.8 // 秋冬销售期
      }
    };
    
    return {
      incomeFactor: seasonalFactors.income[month] || 1.0,
      expenseFactor: seasonalFactors.expense[month] || 1.0
    };
  }

  /**
   * 生成批次相关的财务数据
   */
  generateBatchRelatedData() {
    const batches = [
      'QY-20240301-001',
      'QY-20240215-002', 
      'QY-20240320-003'
    ];
    
    return batches.map(batch => ({
      batchNumber: batch,
      income: Math.round(15000 + Math.random() * 20000),
      expense: Math.round(8000 + Math.random() * 12000),
      profitMargin: null, // 会在后续计算
      status: Math.random() > 0.3 ? 'active' : 'completed',
      dayAge: Math.round(30 + Math.random() * 60),
      headCount: Math.round(300 + Math.random() * 200)
    })).map(batch => ({
      ...batch,
      profit: batch.income - batch.expense,
      profitMargin: batch.income > 0 ? ((batch.income - batch.expense) / batch.income * 100).toFixed(1) : '0.0'
    }));
  }

  /**
   * 生成预测数据
   */
  generateForecastData(months = 6) {
    const forecast = [];
    const baseIncome = 50000;
    const baseExpense = 35000;
    
    for (let i = 1; i <= months; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() + i);
      
      // 考虑季节性因素
      const seasonal = this.generateSeasonalData(date.getMonth() + 1);
      
      // 考虑增长趋势
      const growthFactor = 1 + (i * 0.02); // 每月2%的增长
      
      const income = Math.round(baseIncome * seasonal.incomeFactor * growthFactor);
      const expense = Math.round(baseExpense * seasonal.expenseFactor * growthFactor * 0.95);
      
      forecast.push({
        month: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        monthLabel: `${date.getMonth() + 1}月`,
        income,
        expense,
        profit: income - expense,
        profitMargin: ((income - expense) / income * 100).toFixed(1),
        confidence: Math.max(0.6, 0.9 - i * 0.05) // 随时间降低置信度
      });
    }
    
    return forecast;
  }

  /**
   * 生成风险分析数据
   */
  generateRiskAnalysisData() {
    return {
      riskFactors: [
        {
          factor: '市场价格波动',
          probability: 'high',
          impact: 'medium',
          description: '鹅肉和鹅蛋价格受季节和市场需求影响较大',
          mitigation: '建立多元化销售渠道，签订长期合同'
        },
        {
          factor: '疫病风险',
          probability: 'medium',
          impact: 'high',
          description: '传染性疾病可能导致大量死亡，严重影响收益',
          mitigation: '加强疫苗接种和生物安全管理'
        },
        {
          factor: '饲料成本上涨',
          probability: 'high',
          impact: 'medium',
          description: '原料价格上涨导致饲料成本增加',
          mitigation: '寻找替代原料，提高饲料转化率'
        },
        {
          factor: '政策变化',
          probability: 'low',
          impact: 'high',
          description: '环保政策和行业规范可能影响经营',
          mitigation: '关注政策动向，及时调整经营策略'
        }
      ],
      overallRiskLevel: 'medium',
      riskScore: 65.5,
      recommendations: [
        '建立应急资金储备',
        '购买农业保险',
        '多元化经营降低风险',
        '建立风险监控体系'
      ]
    };
  }

  /**
   * 生成现金流数据
   */
  generateCashFlowData(months = 12) {
    const cashFlow = [];
    let cumulativeCash = 50000; // 期初现金
    
    for (let i = 0; i < months; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - months + i + 1);
      
      const seasonal = this.generateSeasonalData(date.getMonth() + 1);
      const operatingCash = Math.round((15000 * seasonal.incomeFactor) - (12000 * seasonal.expenseFactor));
      const investingCash = i % 6 === 0 ? -5000 : 0; // 每半年一次设备投资
      const financingCash = i === 0 ? 20000 : 0; // 年初贷款
      
      const netCash = operatingCash + investingCash + financingCash;
      cumulativeCash += netCash;
      
      cashFlow.push({
        month: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        monthLabel: `${date.getMonth() + 1}月`,
        operatingCash,
        investingCash,
        financingCash,
        netCash,
        cumulativeCash,
        cashRatio: cumulativeCash / 50000 // 相对于期初的比率
      });
    }
    
    return cashFlow;
  }

  /**
   * 清除模拟数据缓存
   */
  static clearCache() {
    // 这里可以清除任何缓存的模拟数据
    console.log('财务模拟数据缓存已清除');
  }

  /**
   * 获取数据生成时间
   */
  getGenerationTime() {
    return this.dataGenerationTime.toISOString();
  }

  /**
   * 设置随机种子（用于生成一致的测试数据）
   */
  static setSeed(seed) {
    // 简单的伪随机数生成器
    Math.random = function() {
      seed = (seed * 9301 + 49297) % 233280;
      return seed / 233280;
    };
  }
}

module.exports = FinanceMockDataService;