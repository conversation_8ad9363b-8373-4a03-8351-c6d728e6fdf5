// utils/health-finance-integration.js - 健康管理与财务整合服务
class HealthFinanceIntegration {
  constructor() {
    this.storageKey = 'health_finance_integration';
    this.statsKey = 'health_finance_stats';
    this.initialized = false;
  }

  /**
   * 初始化服务
   */
  async init() {
    try {
      if (!this.initialized) {
        await this.initializeDefaultData();
        this.initialized = true;
      }
      return {
        success: true,
        message: '健康财务整合服务初始化完成'
      };
    } catch (error) {
      console.error('初始化健康财务整合服务失败:', error);
      return {
        success: false,
        message: '初始化失败: ' + error.message
      };
    }
  }

  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const existingData = wx.getStorageSync(this.storageKey);
      if (!existingData || Object.keys(existingData).length === 0) {
        const defaultData = this.generateDefaultIntegrationData();
        wx.setStorageSync(this.storageKey, defaultData);
      }

      const existingStats = wx.getStorageSync(this.statsKey);
      if (!existingStats || Object.keys(existingStats).length === 0) {
        const defaultStats = this.generateDefaultStatistics();
        wx.setStorageSync(this.statsKey, defaultStats);
      }
    } catch (error) {
      console.error('初始化默认数据失败:', error);
    }
  }

  /**
   * 生成默认整合数据
   */
  generateDefaultIntegrationData() {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return {
      healthRecords: [
        {
          id: 'health_001',
          batchNumber: 'QY-20240301-001',
          date: '2024-03-15',
          type: 'vaccination',
          description: '小鹅瘟疫苗接种',
          cost: 1200,
          affectedCount: 500,
          medicine: '小鹅瘟疫苗',
          supplier: '动物保健公司',
          createTime: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'health_002',
          batchNumber: 'QY-20240215-002',
          date: '2024-03-08',
          type: 'treatment',
          description: '呼吸道感染治疗',
          cost: 800,
          affectedCount: 15,
          medicine: '抗生素',
          supplier: '兽药经销商',
          createTime: new Date(now.getTime() - 17 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'health_003',
          batchNumber: 'QY-20240320-003',
          date: '2024-03-25',
          type: 'prevention',
          description: '预防性消毒',
          cost: 300,
          affectedCount: 400,
          medicine: '消毒液',
          supplier: '卫生用品公司',
          createTime: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      financeIntegrations: [
        {
          id: 'integration_001',
          healthRecordId: 'health_001',
          financeRecordId: 'expense_health_001',
          integrationTime: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'completed'
        },
        {
          id: 'integration_002',
          healthRecordId: 'health_002',
          financeRecordId: 'expense_health_002',
          integrationTime: new Date(now.getTime() - 17 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'completed'
        }
      ],
      lastUpdateTime: now.toISOString()
    };
  }

  /**
   * 生成默认统计数据
   */
  generateDefaultStatistics() {
    const now = new Date();
    
    return {
      monthlyHealthExpense: {
        current: 2300,
        previous: 1850,
        growth: 24.3
      },
      healthCostByType: [
        { type: 'vaccination', label: '疫苗接种', cost: 1200, percentage: 52.2 },
        { type: 'treatment', label: '疾病治疗', cost: 800, percentage: 34.8 },
        { type: 'prevention', label: '预防保健', cost: 300, percentage: 13.0 }
      ],
      healthCostByBatch: [
        { batch: 'QY-20240301-001', cost: 1200, recordCount: 3 },
        { batch: 'QY-20240215-002', cost: 800, recordCount: 2 },
        { batch: 'QY-20240320-003', cost: 300, recordCount: 1 }
      ],
      healthTrends: [
        { month: '1月', cost: 1600 },
        { month: '2月', cost: 1850 },
        { month: '3月', cost: 2300 },
        { month: '4月', cost: 1950 }
      ],
      healthEfficiency: {
        preventionRatio: 65.5, // 预防费用占比
        treatmentRatio: 34.5,  // 治疗费用占比
        avgCostPerHead: 2.3,   // 平均每只鹅健康成本
        healthROI: 8.5         // 健康投入回报率
      },
      lastCalculateTime: now.toISOString()
    };
  }

  /**
   * 获取健康财务统计数据
   */
  async getHealthFinanceStatistics(params = {}) {
    try {
      await this.init();
      
      const stats = wx.getStorageSync(this.statsKey) || this.generateDefaultStatistics();
      
      // 根据参数筛选和计算数据
      if (params.timeRange || params.startDate || params.endDate) {
        // 这里可以根据时间范围重新计算统计数据
        return this.calculateStatsForDateRange(params);
      }

      // 添加一些实时计算的数据
      const enhancedStats = await this.enhanceStatisticsData(stats);
      
      return enhancedStats;
    } catch (error) {
      console.error('获取健康财务统计失败:', error);
      return this.generateDefaultStatistics();
    }
  }

  /**
   * 增强统计数据
   */
  async enhanceStatisticsData(baseStats) {
    try {
      // 获取最新的健康记录数据
      const integrationData = wx.getStorageSync(this.storageKey) || this.generateDefaultIntegrationData();
      
      // 计算健康成本趋势
      const healthTrends = this.calculateHealthTrends(integrationData.healthRecords);
      
      // 计算成本效率指标
      const efficiencyMetrics = this.calculateEfficiencyMetrics(integrationData.healthRecords);
      
      // 生成健康财务洞察
      const insights = this.generateHealthFinanceInsights(integrationData, baseStats);

      return {
        ...baseStats,
        healthTrends,
        efficiencyMetrics,
        insights,
        summary: this.generateHealthFinanceSummary(baseStats, efficiencyMetrics),
        recommendations: this.generateHealthFinanceRecommendations(baseStats, efficiencyMetrics)
      };
    } catch (error) {
      console.error('增强统计数据失败:', error);
      return baseStats;
    }
  }

  /**
   * 计算健康成本趋势
   */
  calculateHealthTrends(healthRecords) {
    try {
      const monthlyStats = new Map();
      const now = new Date();
      
      // 生成最近6个月的数据
      for (let i = 5; i >= 0; i--) {
        const targetDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthKey = `${targetDate.getFullYear()}-${String(targetDate.getMonth() + 1).padStart(2, '0')}`;
        const monthLabel = `${targetDate.getMonth() + 1}月`;
        
        monthlyStats.set(monthKey, {
          month: monthLabel,
          cost: 0,
          recordCount: 0,
          preventionCost: 0,
          treatmentCost: 0,
          vaccinationCost: 0
        });
      }

      // 汇总健康记录数据
      healthRecords.forEach(record => {
        const recordDate = new Date(record.date);
        const monthKey = `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}`;
        
        if (monthlyStats.has(monthKey)) {
          const stats = monthlyStats.get(monthKey);
          stats.cost += record.cost;
          stats.recordCount += 1;
          
          switch (record.type) {
            case 'prevention':
              stats.preventionCost += record.cost;
              break;
            case 'treatment':
              stats.treatmentCost += record.cost;
              break;
            case 'vaccination':
              stats.vaccinationCost += record.cost;
              break;
          }
        }
      });

      return Array.from(monthlyStats.values());
    } catch (error) {
      console.error('计算健康趋势失败:', error);
      return [];
    }
  }

  /**
   * 计算效率指标
   */
  calculateEfficiencyMetrics(healthRecords) {
    try {
      const totalCost = healthRecords.reduce((sum, record) => sum + record.cost, 0);
      const totalAnimals = healthRecords.reduce((sum, record) => sum + record.affectedCount, 0);
      
      const preventionCost = healthRecords
        .filter(record => record.type === 'prevention' || record.type === 'vaccination')
        .reduce((sum, record) => sum + record.cost, 0);
      
      const treatmentCost = healthRecords
        .filter(record => record.type === 'treatment')
        .reduce((sum, record) => sum + record.cost, 0);

      const preventionRatio = totalCost > 0 ? (preventionCost / totalCost * 100).toFixed(1) : 0;
      const treatmentRatio = totalCost > 0 ? (treatmentCost / totalCost * 100).toFixed(1) : 0;
      const avgCostPerHead = totalAnimals > 0 ? (totalCost / totalAnimals).toFixed(2) : 0;

      return {
        totalHealthCost: totalCost,
        preventionCost,
        treatmentCost,
        preventionRatio: parseFloat(preventionRatio),
        treatmentRatio: parseFloat(treatmentRatio),
        avgCostPerHead: parseFloat(avgCostPerHead),
        recordCount: healthRecords.length,
        affectedAnimals: totalAnimals,
        healthROI: this.calculateHealthROI(preventionCost, treatmentCost)
      };
    } catch (error) {
      console.error('计算效率指标失败:', error);
      return {
        totalHealthCost: 0,
        preventionCost: 0,
        treatmentCost: 0,
        preventionRatio: 0,
        treatmentRatio: 0,
        avgCostPerHead: 0,
        recordCount: 0,
        affectedAnimals: 0,
        healthROI: 0
      };
    }
  }

  /**
   * 计算健康投资回报率
   */
  calculateHealthROI(preventionCost, treatmentCost) {
    try {
      if (preventionCost === 0) return 0;
      
      // 假设预防成本能够减少治疗成本
      // ROI = (避免的治疗成本 - 预防投入) / 预防投入 * 100
      const avoidedTreatmentCost = preventionCost * 3; // 假设1元预防成本能避免3元治疗成本
      const roi = ((avoidedTreatmentCost - preventionCost) / preventionCost * 100);
      
      return Math.max(0, roi.toFixed(1));
    } catch (error) {
      console.error('计算健康ROI失败:', error);
      return 0;
    }
  }

  /**
   * 生成健康财务洞察
   */
  generateHealthFinanceInsights(integrationData, stats) {
    const insights = [];
    
    try {
      // 预防与治疗成本比例分析
      if (stats.healthEfficiency && stats.healthEfficiency.preventionRatio > 60) {
        insights.push({
          type: 'positive',
          icon: '✅',
          title: '预防投入充足',
          description: `预防费用占比${stats.healthEfficiency.preventionRatio}%，有效降低了疾病治疗成本`
        });
      } else if (stats.healthEfficiency && stats.healthEfficiency.preventionRatio < 40) {
        insights.push({
          type: 'warning',
          icon: '⚠️',
          title: '预防投入不足',
          description: `预防费用占比仅${stats.healthEfficiency.preventionRatio}%，建议增加预防保健投入`
        });
      }

      // 成本趋势分析
      if (stats.monthlyHealthExpense && stats.monthlyHealthExpense.growth > 30) {
        insights.push({
          type: 'attention',
          icon: '📈',
          title: '健康成本快速上升',
          description: `本月健康成本较上月增长${stats.monthlyHealthExpense.growth}%，需关注疾病防控`
        });
      }

      // 批次成本分析
      const healthRecords = integrationData.healthRecords || [];
      const batchCostMap = new Map();
      
      healthRecords.forEach(record => {
        if (batchCostMap.has(record.batchNumber)) {
          batchCostMap.set(record.batchNumber, batchCostMap.get(record.batchNumber) + record.cost);
        } else {
          batchCostMap.set(record.batchNumber, record.cost);
        }
      });
      
      if (batchCostMap.size > 0) {
        const maxCostBatch = Array.from(batchCostMap.entries())
          .sort((a, b) => b[1] - a[1])[0];
        
        if (maxCostBatch[1] > 1000) {
          insights.push({
            type: 'info',
            icon: '💰',
            title: '高成本批次关注',
            description: `批次 ${maxCostBatch[0]} 健康成本最高，达 ¥${maxCostBatch[1].toFixed(2)}`
          });
        }
      }

      // 效率指标分析
      if (stats.healthEfficiency && stats.healthEfficiency.avgCostPerHead < 2) {
        insights.push({
          type: 'positive',
          icon: '🎯',
          title: '健康成本控制良好',
          description: `平均每只鹅健康成本 ¥${stats.healthEfficiency.avgCostPerHead}，控制在合理范围内`
        });
      }

    } catch (error) {
      console.error('生成健康财务洞察失败:', error);
    }

    return insights.slice(0, 4); // 最多返回4个洞察
  }

  /**
   * 生成健康财务摘要
   */
  generateHealthFinanceSummary(stats, metrics) {
    try {
      const currentMonthCost = stats.monthlyHealthExpense?.current || 0;
      const preventionRatio = metrics.preventionRatio || 0;
      const avgCostPerHead = metrics.avgCostPerHead || 0;

      let summary = '';
      
      if (preventionRatio > 60 && avgCostPerHead < 3) {
        summary = `健康管理财务状况良好。本月健康投入 ¥${currentMonthCost.toFixed(2)}，预防费用占比${preventionRatio}%，平均每只鹅健康成本 ¥${avgCostPerHead}，成本控制合理。`;
      } else if (preventionRatio > 45) {
        summary = `健康管理投入适中。本月支出 ¥${currentMonthCost.toFixed(2)}，预防投入占比${preventionRatio}%，建议继续加强预防保健措施。`;
      } else {
        summary = `健康管理需要优化。本月支出 ¥${currentMonthCost.toFixed(2)}，预防投入比例偏低(${preventionRatio}%)，建议增加预防性投入以降低治疗成本。`;
      }

      return summary;
    } catch (error) {
      console.error('生成健康财务摘要失败:', error);
      return '健康财务数据统计中...';
    }
  }

  /**
   * 生成健康财务建议
   */
  generateHealthFinanceRecommendations(stats, metrics) {
    const recommendations = [];
    
    try {
      // 预防投入建议
      if (metrics.preventionRatio < 50) {
        recommendations.push({
          priority: 'high',
          title: '增加预防投入',
          description: '当前预防费用占比偏低，建议增加疫苗接种和预防保健投入',
          expectedBenefit: '预计可降低治疗成本20-30%'
        });
      }

      // 成本控制建议
      if (metrics.avgCostPerHead > 3) {
        recommendations.push({
          priority: 'medium',
          title: '优化健康成本',
          description: '平均每只鹅健康成本偏高，建议优化用药方案和供应商选择',
          expectedBenefit: '预计可降低单头健康成本15-25%'
        });
      }

      // 批次管理建议
      if (stats.healthCostByBatch && stats.healthCostByBatch.length > 0) {
        const highCostBatches = stats.healthCostByBatch.filter(batch => batch.cost > 1000);
        if (highCostBatches.length > 0) {
          recommendations.push({
            priority: 'medium',
            title: '重点批次健康管理',
            description: '部分批次健康成本较高，建议加强这些批次的健康监控和预防措施',
            expectedBenefit: '预计可提高整体健康水平'
          });
        }
      }

      // ROI优化建议
      if (metrics.healthROI < 5) {
        recommendations.push({
          priority: 'low',
          title: '提高健康投资回报',
          description: '通过精准化健康管理，提高预防措施的针对性和有效性',
          expectedBenefit: '预计健康投资回报率提升至8%以上'
        });
      }

    } catch (error) {
      console.error('生成健康财务建议失败:', error);
    }

    return recommendations.slice(0, 3); // 最多返回3个建议
  }

  /**
   * 根据日期范围计算统计数据
   */
  async calculateStatsForDateRange(params) {
    try {
      const integrationData = wx.getStorageSync(this.storageKey) || this.generateDefaultIntegrationData();
      const healthRecords = integrationData.healthRecords || [];

      // 过滤日期范围内的记录
      let filteredRecords = healthRecords;
      
      if (params.startDate && params.endDate) {
        filteredRecords = healthRecords.filter(record => {
          const recordDate = new Date(record.date);
          return recordDate >= new Date(params.startDate) && recordDate <= new Date(params.endDate);
        });
      }

      // 重新计算统计数据
      const totalCost = filteredRecords.reduce((sum, record) => sum + record.cost, 0);
      const costByType = this.calculateCostByType(filteredRecords);
      const costByBatch = this.calculateCostByBatch(filteredRecords);
      const efficiencyMetrics = this.calculateEfficiencyMetrics(filteredRecords);

      return {
        monthlyHealthExpense: {
          current: totalCost,
          previous: totalCost * 0.85, // 模拟上期数据
          growth: 15
        },
        healthCostByType: costByType,
        healthCostByBatch: costByBatch,
        healthEfficiency: efficiencyMetrics,
        dateRange: {
          startDate: params.startDate,
          endDate: params.endDate
        },
        lastCalculateTime: new Date().toISOString()
      };
    } catch (error) {
      console.error('计算日期范围统计失败:', error);
      return this.generateDefaultStatistics();
    }
  }

  /**
   * 按类型计算成本
   */
  calculateCostByType(records) {
    const typeMap = new Map();
    const totalCost = records.reduce((sum, record) => sum + record.cost, 0);

    records.forEach(record => {
      if (typeMap.has(record.type)) {
        typeMap.set(record.type, typeMap.get(record.type) + record.cost);
      } else {
        typeMap.set(record.type, record.cost);
      }
    });

    const typeLabels = {
      'vaccination': '疫苗接种',
      'treatment': '疾病治疗',
      'prevention': '预防保健',
      'medicine': '药品采购',
      'inspection': '健康检查'
    };

    return Array.from(typeMap.entries()).map(([type, cost]) => ({
      type,
      label: typeLabels[type] || type,
      cost,
      percentage: totalCost > 0 ? (cost / totalCost * 100).toFixed(1) : 0
    }));
  }

  /**
   * 按批次计算成本
   */
  calculateCostByBatch(records) {
    const batchMap = new Map();

    records.forEach(record => {
      if (batchMap.has(record.batchNumber)) {
        const existing = batchMap.get(record.batchNumber);
        existing.cost += record.cost;
        existing.recordCount += 1;
      } else {
        batchMap.set(record.batchNumber, {
          batch: record.batchNumber,
          cost: record.cost,
          recordCount: 1
        });
      }
    });

    return Array.from(batchMap.values());
  }

  /**
   * 更新健康财务整合数据
   */
  async updateHealthFinanceIntegration(healthRecord, financeRecord) {
    try {
      const integrationData = wx.getStorageSync(this.storageKey) || this.generateDefaultIntegrationData();

      // 添加健康记录
      integrationData.healthRecords.unshift({
        id: healthRecord.id,
        batchNumber: healthRecord.batchNumber,
        date: healthRecord.date,
        type: healthRecord.recordType || healthRecord.type,
        description: healthRecord.description,
        cost: parseFloat(healthRecord.cost) || 0,
        affectedCount: parseInt(healthRecord.affectedCount) || 0,
        medicine: healthRecord.medicine || '',
        supplier: healthRecord.supplier || '',
        createTime: new Date().toISOString()
      });

      // 添加整合记录
      integrationData.financeIntegrations.unshift({
        id: `integration_${Date.now()}`,
        healthRecordId: healthRecord.id,
        financeRecordId: financeRecord ? financeRecord.id : null,
        integrationTime: new Date().toISOString(),
        status: 'completed'
      });

      integrationData.lastUpdateTime = new Date().toISOString();
      wx.setStorageSync(this.storageKey, integrationData);

      // 重新计算统计数据
      await this.recalculateStatistics();

      return {
        success: true,
        message: '健康财务整合数据已更新'
      };
    } catch (error) {
      console.error('更新健康财务整合数据失败:', error);
      return {
        success: false,
        message: '更新失败: ' + error.message
      };
    }
  }

  /**
   * 重新计算统计数据
   */
  async recalculateStatistics() {
    try {
      const integrationData = wx.getStorageSync(this.storageKey) || this.generateDefaultIntegrationData();
      const newStats = this.calculateCurrentStatistics(integrationData);
      wx.setStorageSync(this.statsKey, newStats);
      return true;
    } catch (error) {
      console.error('重新计算统计数据失败:', error);
      return false;
    }
  }

  /**
   * 计算当前统计数据
   */
  calculateCurrentStatistics(integrationData) {
    try {
      const healthRecords = integrationData.healthRecords || [];
      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

      // 本月健康费用
      const currentMonthRecords = healthRecords.filter(record => {
        const recordDate = new Date(record.date);
        return recordDate >= thisMonth;
      });

      // 上月健康费用
      const previousMonthRecords = healthRecords.filter(record => {
        const recordDate = new Date(record.date);
        return recordDate >= lastMonth && recordDate < thisMonth;
      });

      const currentMonthCost = currentMonthRecords.reduce((sum, record) => sum + record.cost, 0);
      const previousMonthCost = previousMonthRecords.reduce((sum, record) => sum + record.cost, 0);
      const growth = previousMonthCost > 0 ? ((currentMonthCost - previousMonthCost) / previousMonthCost * 100).toFixed(1) : 0;

      return {
        monthlyHealthExpense: {
          current: currentMonthCost,
          previous: previousMonthCost,
          growth: parseFloat(growth)
        },
        healthCostByType: this.calculateCostByType(healthRecords),
        healthCostByBatch: this.calculateCostByBatch(healthRecords),
        healthTrends: this.calculateHealthTrends(healthRecords),
        healthEfficiency: this.calculateEfficiencyMetrics(healthRecords),
        lastCalculateTime: new Date().toISOString()
      };
    } catch (error) {
      console.error('计算当前统计数据失败:', error);
      return this.generateDefaultStatistics();
    }
  }

  /**
   * 清除所有健康财务整合数据（测试用）
   */
  clearAllData() {
    try {
      wx.removeStorageSync(this.storageKey);
      wx.removeStorageSync(this.statsKey);
      this.initialized = false;
      return {
        success: true,
        message: '健康财务整合数据清除成功'
      };
    } catch (error) {
      console.error('清除数据失败:', error);
      return {
        success: false,
        message: '清除数据失败'
      };
    }
  }
}

// 导出单例实例
const healthFinanceIntegration = new HealthFinanceIntegration();

module.exports = {
  healthFinanceIntegration,
  HealthFinanceIntegrationClass: HealthFinanceIntegration
};