// utils/auth-helper.js - 权限认证辅助工具
class AuthHelper {
  constructor() {
    this.userStorageKey = 'current_user';
    this.permissionStorageKey = 'user_permissions';
    this.sessionKey = 'user_session';
  }

  /**
   * 检查页面权限
   * @param {Object} options - 页面options参数
   * @param {string} requiredPermission - 所需权限
   * @param {Function} onSuccess - 权限检查通过回调
   * @param {Function} onFail - 权限检查失败回调
   */
  checkPagePermission(options, requiredPermission, onSuccess, onFail) {
    try {
      // 获取当前用户信息
      const currentUser = this.getCurrentUser();
      
      if (!currentUser) {
        console.warn('用户未登录，跳转到登录页面');
        this.redirectToLogin();
        if (onFail) onFail('用户未登录');
        return false;
      }

      // 检查权限
      const hasPermission = this.hasPermission(requiredPermission);
      
      if (!hasPermission) {
        console.warn(`用户权限不足，需要权限: ${requiredPermission}`);
        this.showPermissionDeniedDialog(requiredPermission);
        if (onFail) onFail(`权限不足: ${requiredPermission}`);
        return false;
      }

      console.log(`权限检查通过，用户: ${currentUser.username}, 权限: ${requiredPermission}`);
      if (onSuccess) onSuccess();
      return true;

    } catch (error) {
      console.error('权限检查出错:', error);
      if (onFail) onFail(error.message);
      return false;
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    try {
      const userData = wx.getStorageSync(this.userStorageKey);
      if (!userData) {
        return this.createDefaultUser();
      }
      return userData;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return this.createDefaultUser();
    }
  }

  /**
   * 创建默认用户（用于测试）
   */
  createDefaultUser() {
    const defaultUser = {
      id: 1,
      username: 'test_user',
      name: '测试用户',
      role: 'admin',
      permissions: [
        'production:environment:read',
        'production:environment:write',
        'production:records:read',
        'production:records:write',
        'finance:reports:read',
        'finance:reports:write',
        'health:records:read',
        'health:records:write'
      ],
      loginTime: new Date().toISOString()
    };

    // 保存默认用户信息
    try {
      wx.setStorageSync(this.userStorageKey, defaultUser);
    } catch (error) {
      console.error('保存默认用户信息失败:', error);
    }

    return defaultUser;
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission) {
    try {
      const currentUser = this.getCurrentUser();
      
      if (!currentUser || !currentUser.permissions) {
        return false;
      }

      // 超级管理员拥有所有权限
      if (currentUser.role === 'super_admin') {
        return true;
      }

      // 检查具体权限
      return currentUser.permissions.includes(permission);
    } catch (error) {
      console.error('权限检查失败:', error);
      return false;
    }
  }

  /**
   * 检查是否有角色权限
   */
  hasRole(role) {
    try {
      const currentUser = this.getCurrentUser();
      return currentUser && currentUser.role === role;
    } catch (error) {
      console.error('角色检查失败:', error);
      return false;
    }
  }

  /**
   * 设置用户信息
   */
  setUser(userData) {
    try {
      wx.setStorageSync(this.userStorageKey, userData);
      return true;
    } catch (error) {
      console.error('设置用户信息失败:', error);
      return false;
    }
  }

  /**
   * 用户登录
   */
  login(username, password, role = 'user') {
    try {
      // 这里应该调用实际的登录API
      // 现在提供模拟登录功能
      const userData = {
        id: Date.now(),
        username: username,
        name: username,
        role: role,
        permissions: this.getPermissionsByRole(role),
        loginTime: new Date().toISOString(),
        sessionId: this.generateSessionId()
      };

      this.setUser(userData);
      wx.setStorageSync(this.sessionKey, userData.sessionId);

      return {
        success: true,
        message: '登录成功',
        data: userData
      };
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: '登录失败'
      };
    }
  }

  /**
   * 用户退出登录
   */
  logout() {
    try {
      wx.removeStorageSync(this.userStorageKey);
      wx.removeStorageSync(this.sessionKey);
      wx.removeStorageSync(this.permissionStorageKey);
      
      return {
        success: true,
        message: '退出登录成功'
      };
    } catch (error) {
      console.error('退出登录失败:', error);
      return {
        success: false,
        message: '退出登录失败'
      };
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    try {
      const currentUser = wx.getStorageSync(this.userStorageKey);
      const sessionId = wx.getStorageSync(this.sessionKey);
      
      return !!(currentUser && sessionId);
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  }

  /**
   * 根据角色获取权限列表
   */
  getPermissionsByRole(role) {
    const rolePermissions = {
      'super_admin': ['*'], // 所有权限
      'admin': [
        'production:environment:read',
        'production:environment:write',
        'production:records:read',
        'production:records:write',
        'finance:reports:read',
        'finance:reports:write',
        'health:records:read',
        'health:records:write',
        'system:settings:read',
        'system:settings:write'
      ],
      'manager': [
        'production:environment:read',
        'production:environment:write',
        'production:records:read',
        'production:records:write',
        'finance:reports:read',
        'health:records:read',
        'health:records:write'
      ],
      'employee': [
        'production:environment:read',
        'production:records:read',
        'health:records:read'
      ],
      'user': [
        'production:environment:read'
      ]
    };

    return rolePermissions[role] || [];
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后再使用该功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 如果有登录页面，跳转到登录页面
          // wx.navigateTo({
          //   url: '/pages/login/login'
          // });
          
          // 暂时提供模拟登录
          this.showMockLogin();
        }
      }
    });
  }

  /**
   * 显示模拟登录对话框（用于测试）
   */
  showMockLogin() {
    wx.showActionSheet({
      itemList: ['管理员登录', '经理登录', '员工登录'],
      success: (res) => {
        const roles = ['admin', 'manager', 'employee'];
        const role = roles[res.tapIndex];
        
        const loginResult = this.login(`test_${role}`, '123456', role);
        
        if (loginResult.success) {
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          // 刷新当前页面
          setTimeout(() => {
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            if (currentPage && currentPage.onLoad) {
              currentPage.onLoad(currentPage.options || {});
            }
          }, 1500);
        }
      }
    });
  }

  /**
   * 显示权限不足对话框
   */
  showPermissionDeniedDialog(requiredPermission) {
    const currentUser = this.getCurrentUser();
    
    wx.showModal({
      title: '权限不足',
      content: `当前用户: ${currentUser ? currentUser.name : '未登录'}\n当前角色: ${currentUser ? currentUser.role : '无'}\n所需权限: ${requiredPermission}\n\n请联系管理员获取相应权限`,
      confirmText: '切换用户',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.showMockLogin();
        }
      }
    });
  }

  /**
   * 权限装饰器（用于页面方法）
   */
  requirePermission(permission) {
    return (target, propertyKey, descriptor) => {
      const originalMethod = descriptor.value;
      
      descriptor.value = function(...args) {
        const authHelper = new AuthHelper();
        
        if (authHelper.hasPermission(permission)) {
          return originalMethod.apply(this, args);
        } else {
          authHelper.showPermissionDeniedDialog(permission);
          return false;
        }
      };
      
      return descriptor;
    };
  }

  /**
   * 获取权限列表
   */
  getUserPermissions() {
    try {
      const currentUser = this.getCurrentUser();
      return currentUser ? currentUser.permissions || [] : [];
    } catch (error) {
      console.error('获取权限列表失败:', error);
      return [];
    }
  }

  /**
   * 检查多个权限（或关系）
   */
  hasAnyPermission(permissions) {
    try {
      return permissions.some(permission => this.hasPermission(permission));
    } catch (error) {
      console.error('检查多权限失败:', error);
      return false;
    }
  }

  /**
   * 检查多个权限（与关系）
   */
  hasAllPermissions(permissions) {
    try {
      return permissions.every(permission => this.hasPermission(permission));
    } catch (error) {
      console.error('检查多权限失败:', error);
      return false;
    }
  }

  /**
   * 更新用户权限
   */
  updateUserPermissions(permissions) {
    try {
      const currentUser = this.getCurrentUser();
      if (currentUser) {
        currentUser.permissions = permissions;
        currentUser.updateTime = new Date().toISOString();
        this.setUser(currentUser);
      }
      return true;
    } catch (error) {
      console.error('更新用户权限失败:', error);
      return false;
    }
  }

  /**
   * 清除所有认证信息（测试用）
   */
  clearAuth() {
    try {
      wx.removeStorageSync(this.userStorageKey);
      wx.removeStorageSync(this.permissionStorageKey);
      wx.removeStorageSync(this.sessionKey);
      return true;
    } catch (error) {
      console.error('清除认证信息失败:', error);
      return false;
    }
  }
}

// 导出单例实例
const authHelper = new AuthHelper();

module.exports = authHelper;