#!/usr/bin/env node

/**
 * 综合主包优化工具
 * 基于深度分析的安全清理方案
 */

const fs = require('fs');
const path = require('path');

class ComprehensivePackageOptimizer {
  constructor() {
    this.rootPath = process.cwd();
    this.optimizations = [];
    this.warnings = [];
    this.totalSizeSaved = 0;
  }

  /**
   * 执行综合优化
   */
  async optimize() {
    console.log('🚀 开始综合主包优化...\n');

    // 1. 清理备份文件夹
    await this.cleanupBackupDirectories();

    // 2. 清理重复的样式文件
    await this.cleanupDuplicateStyles();

    // 3. 优化图片资源
    await this.optimizeImageResources();

    // 4. 清理测试和临时文件
    await this.cleanupTestAndTempFiles();

    // 5. 检查大文件
    await this.analyzeLargeFiles();

    // 6. 生成优化报告
    this.generateOptimizationReport();

    console.log('\n✅ 综合主包优化完成！');
  }

  /**
   * 清理备份文件夹
   */
  async cleanupBackupDirectories() {
    console.log('🗂️  分析备份文件夹...');

    const backupDirs = [
      'backup/20250829_231147',
      'backup/20250829_231154',
      'backup/quality-fix',
      'cleanup_backup'
    ];

    let totalBackupSize = 0;

    for (const dir of backupDirs) {
      const fullPath = path.join(this.rootPath, dir);
      if (fs.existsSync(fullPath)) {
        const size = this.calculateDirectorySize(fullPath);
        totalBackupSize += size;
        console.log(`   📁 ${dir}: ${this.formatSize(size)}`);
      }
    }

    console.log(`   📊 备份文件夹总大小: ${this.formatSize(totalBackupSize)}`);
    
    if (totalBackupSize > 0) {
      this.warnings.push({
        type: '备份文件夹',
        message: `发现 ${this.formatSize(totalBackupSize)} 的备份文件，建议在确认项目稳定后清理`,
        action: '手动确认后可删除备份文件夹'
      });
    }
  }

  /**
   * 清理重复的样式文件
   */
  async cleanupDuplicateStyles() {
    console.log('🎨 分析样式文件...');

    const styleFiles = this.findFilesByExtension('.wxss');
    const duplicateStyles = [];

    // 检查可能重复的样式文件
    const potentialDuplicates = [
      'styles/oa-common.wxss',
      'styles/oa-enhanced.wxss',
      'styles/oa-loading.wxss'
    ];

    for (const file of potentialDuplicates) {
      const fullPath = path.join(this.rootPath, file);
      if (fs.existsSync(fullPath)) {
        const size = fs.statSync(fullPath).size;
        console.log(`   📄 ${file}: ${this.formatSize(size)}`);
        
        // 检查是否被引用
        const isUsed = await this.checkStyleFileUsage(file);
        if (!isUsed) {
          duplicateStyles.push({
            file,
            size,
            reason: '样式文件未被引用'
          });
        }
      }
    }

    if (duplicateStyles.length > 0) {
      console.log('   ⚠️  发现可能未使用的样式文件:');
      duplicateStyles.forEach(item => {
        console.log(`      - ${item.file} (${this.formatSize(item.size)})`);
      });
    } else {
      console.log('   ✅ 未发现重复的样式文件');
    }
  }

  /**
   * 优化图片资源
   */
  async optimizeImageResources() {
    console.log('🖼️  分析图片资源...');

    const imageFiles = [
      ...this.findFilesByExtension('.png'),
      ...this.findFilesByExtension('.jpg'),
      ...this.findFilesByExtension('.jpeg'),
      ...this.findFilesByExtension('.svg')
    ];

    let totalImageSize = 0;
    const largeImages = [];

    imageFiles.forEach(file => {
      const size = fs.statSync(file).size;
      totalImageSize += size;
      
      if (size > 50 * 1024) { // 大于50KB的图片
        largeImages.push({
          file: path.relative(this.rootPath, file),
          size
        });
      }
    });

    console.log(`   📊 图片文件总数: ${imageFiles.length}`);
    console.log(`   📊 图片总大小: ${this.formatSize(totalImageSize)}`);

    if (largeImages.length > 0) {
      console.log('   📋 大图片文件 (>50KB):');
      largeImages.sort((a, b) => b.size - a.size).slice(0, 10).forEach(img => {
        console.log(`      - ${img.file}: ${this.formatSize(img.size)}`);
      });

      this.warnings.push({
        type: '图片优化',
        message: `发现 ${largeImages.length} 个大图片文件`,
        action: '建议压缩图片或转换为WebP格式'
      });
    }
  }

  /**
   * 清理测试和临时文件
   */
  async cleanupTestAndTempFiles() {
    console.log('🧪 分析测试和临时文件...');

    const testFiles = [
      ...this.findFilesByPattern(/\.test\.js$/),
      ...this.findFilesByPattern(/\.spec\.js$/),
      ...this.findFilesByPattern(/test-.*\.js$/),
      ...this.findFilesByPattern(/.*-test\.js$/)
    ];

    const tempFiles = [
      ...this.findFilesByPattern(/\.tmp$/),
      ...this.findFilesByPattern(/\.temp$/),
      ...this.findFilesByPattern(/~$/),
      ...this.findFilesByPattern(/\.bak$/)
    ];

    let testSize = 0;
    let tempSize = 0;

    testFiles.forEach(file => {
      if (fs.existsSync(file)) {
        testSize += fs.statSync(file).size;
      }
    });

    tempFiles.forEach(file => {
      if (fs.existsSync(file)) {
        tempSize += fs.statSync(file).size;
      }
    });

    console.log(`   🧪 测试文件: ${testFiles.length} 个, ${this.formatSize(testSize)}`);
    console.log(`   📄 临时文件: ${tempFiles.length} 个, ${this.formatSize(tempSize)}`);

    if (tempFiles.length > 0) {
      console.log('   🗑️  可安全删除的临时文件:');
      tempFiles.forEach(file => {
        const relativePath = path.relative(this.rootPath, file);
        console.log(`      - ${relativePath}`);
      });
    }
  }

  /**
   * 分析大文件
   */
  async analyzeLargeFiles() {
    console.log('📊 分析大文件...');

    const allFiles = [
      ...this.findFilesByExtension('.js'),
      ...this.findFilesByExtension('.wxss'),
      ...this.findFilesByExtension('.json')
    ];

    const largeFiles = [];

    allFiles.forEach(file => {
      const size = fs.statSync(file).size;
      if (size > 30 * 1024) { // 大于30KB
        largeFiles.push({
          file: path.relative(this.rootPath, file),
          size
        });
      }
    });

    largeFiles.sort((a, b) => b.size - a.size);

    console.log('   📋 大文件列表 (>30KB):');
    largeFiles.slice(0, 15).forEach(item => {
      console.log(`      - ${item.file}: ${this.formatSize(item.size)}`);
    });

    // 分析主包中的大文件
    const mainPackageLargeFiles = largeFiles.filter(file => 
      !file.file.includes('backup/') && 
      !file.file.includes('tests/') &&
      !file.file.includes('docs/')
    );

    if (mainPackageLargeFiles.length > 0) {
      this.warnings.push({
        type: '大文件优化',
        message: `主包中有 ${mainPackageLargeFiles.length} 个大文件`,
        action: '考虑拆分大文件或移至分包'
      });
    }
  }

  /**
   * 工具方法
   */
  findFilesByExtension(ext) {
    const files = [];
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.')) {
          walkDir(fullPath);
        } else if (stat.isFile() && item.endsWith(ext)) {
          files.push(fullPath);
        }
      });
    };
    
    walkDir(this.rootPath);
    return files;
  }

  findFilesByPattern(pattern) {
    const files = [];
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.')) {
          walkDir(fullPath);
        } else if (stat.isFile() && pattern.test(item)) {
          files.push(fullPath);
        }
      });
    };
    
    walkDir(this.rootPath);
    return files;
  }

  calculateDirectorySize(dirPath) {
    let totalSize = 0;
    
    if (!fs.existsSync(dirPath)) return 0;
    
    const items = fs.readdirSync(dirPath);
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        totalSize += this.calculateDirectorySize(fullPath);
      } else {
        totalSize += stat.size;
      }
    });
    
    return totalSize;
  }

  async checkStyleFileUsage(styleFile) {
    // 简化的使用检查 - 在实际项目中需要更复杂的分析
    const content = fs.readFileSync(path.join(this.rootPath, styleFile), 'utf8');
    return content.length > 100; // 简单判断：内容较多的文件可能在使用
  }

  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成优化报告
   */
  generateOptimizationReport() {
    console.log('\n📋 综合优化报告');
    console.log('=' .repeat(50));

    if (this.optimizations.length > 0) {
      console.log('\n✅ 已完成的优化:');
      this.optimizations.forEach(opt => {
        console.log(`   ${opt.type}: ${opt.message}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  优化建议:');
      this.warnings.forEach(warning => {
        console.log(`   ${warning.type}: ${warning.message}`);
        console.log(`      建议: ${warning.action}`);
      });
    }

    console.log('\n💡 主包优化最佳实践:');
    console.log('   1. 定期清理备份文件夹');
    console.log('   2. 压缩和优化图片资源');
    console.log('   3. 将大文件移至分包');
    console.log('   4. 移除未使用的样式和脚本');
    console.log('   5. 使用微信开发者工具的代码质量检查');
  }
}

// 执行优化
if (require.main === module) {
  const optimizer = new ComprehensivePackageOptimizer();
  optimizer.optimize().catch(console.error);
}

module.exports = ComprehensivePackageOptimizer;
