#!/usr/bin/env node

// scripts/fix-quality-issues.js - 修复代码质量检查中发现的问题
const fs = require('fs');
const path = require('path');

class QualityIssueFixer {
  constructor() {
    this.rootPath = process.cwd();
    this.fixedIssues = [];
    this.errors = [];
    this.sizeSavings = 0;
  }

  /**
   * 执行所有修复操作
   */
  async fixAllIssues() {
    console.log('🔧 开始修复代码质量问题...\n');
    
    try {
      // 1. 移除未使用的JS文件
      await this.removeUnusedJSFiles();
      
      // 2. 移除未使用的组件声明
      await this.removeUnusedComponentDeclarations();
      
      // 3. 删除未使用的组件文件
      await this.removeUnusedComponentFiles();
      
      // 4. 优化主包大小
      await this.optimizeMainPackageSize();
      
      // 5. 生成修复报告
      this.generateFixReport();
      
    } catch (error) {
      console.error('修复过程中出现错误:', error);
      this.errors.push(`修复失败: ${error.message}`);
    }
  }

  /**
   * 移除未使用的JS文件
   */
  async removeUnusedJSFiles() {
    console.log('🗑️  移除未使用的JS文件...');
    
    const unusedFiles = [
      'utils/business/patch-management.js',
      'utils/business/finance-mock-data.js',
      'utils/business/finance-service.js', 
      'utils/business/health-finance-integration.js',
      'utils/helpers/auth-helper.js',
      'utils/helpers/permission-diagnostic.js',
      'utils/payment/index.js'
    ];

    for (const filePath of unusedFiles) {
      const fullPath = path.join(this.rootPath, filePath);
      
      if (fs.existsSync(fullPath)) {
        // 检查文件是否真的未被使用
        const isUsed = await this.checkFileUsage(filePath);
        
        if (!isUsed) {
          // 备份文件到backup目录
          await this.backupFile(filePath);
          
          // 删除文件
          fs.unlinkSync(fullPath);
          this.fixedIssues.push(`✅ 删除未使用文件: ${filePath}`);
          this.sizeSavings += this.getFileSize(fullPath);
        } else {
          console.log(`⚠️  文件 ${filePath} 仍在使用中，跳过删除`);
        }
      }
    }
  }

  /**
   * 检查文件是否被使用
   */
  async checkFileUsage(filePath) {
    const fileName = path.basename(filePath, '.js');
    const searchPatterns = [
      `require('${filePath}')`,
      `require('./${filePath}')`,
      `require('../../${filePath}')`,
      `require('../../../${filePath}')`,
      `require('../../../../${filePath}')`,
      `from '${filePath}'`,
      `from './${filePath}'`,
      fileName
    ];

    // 搜索所有JS文件
    const jsFiles = this.findJSFiles();
    
    for (const jsFile of jsFiles) {
      if (jsFile === path.join(this.rootPath, filePath)) continue;
      
      try {
        const content = fs.readFileSync(jsFile, 'utf8');
        
        for (const pattern of searchPatterns) {
          if (content.includes(pattern)) {
            return true;
          }
        }
      } catch (error) {
        // 忽略读取错误
      }
    }
    
    return false;
  }

  /**
   * 移除未使用的组件声明
   */
  async removeUnusedComponentDeclarations() {
    console.log('🧹 移除未使用的组件声明...');
    
    const unusedComponents = [
      {
        file: 'pages/shop/shop.json',
        components: ['c-section-header', 'c-list-item', 'c-card', 'c-empty-state', 'c-loading']
      },
      {
        file: 'pages/workspace/workspace.json', 
        components: ['data-card']
      },
      {
        file: 'pages/dev-tools/permission-performance-test/permission-performance-test.json',
        components: ['permission-check']
      },
      {
        file: 'pages/workspace/activity/apply/apply.json',
        components: ['permission-check']
      }
    ];

    for (const item of unusedComponents) {
      const jsonPath = path.join(this.rootPath, item.file);
      
      if (fs.existsSync(jsonPath)) {
        try {
          const config = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
          
          if (config.usingComponents) {
            let modified = false;
            
            for (const componentName of item.components) {
              if (config.usingComponents[componentName]) {
                // 验证组件确实未使用
                const wxmlPath = jsonPath.replace('.json', '.wxml');
                if (fs.existsSync(wxmlPath)) {
                  const wxmlContent = fs.readFileSync(wxmlPath, 'utf8');
                  const tagPattern = new RegExp(`<${componentName}[^>]*>`, 'g');
                  const selfClosingPattern = new RegExp(`<${componentName}[^>]*/>`, 'g');
                  
                  if (!tagPattern.test(wxmlContent) && !selfClosingPattern.test(wxmlContent)) {
                    delete config.usingComponents[componentName];
                    modified = true;
                    this.fixedIssues.push(`✅ 移除未使用组件: ${item.file} -> ${componentName}`);
                  }
                }
              }
            }
            
            if (modified) {
              fs.writeFileSync(jsonPath, JSON.stringify(config, null, 2));
              this.sizeSavings += 1; // 每个组件约1KB
            }
          }
        } catch (error) {
          this.errors.push(`处理 ${item.file} 失败: ${error.message}`);
        }
      }
    }
  }

  /**
   * 删除未使用的组件文件
   */
  async removeUnusedComponentFiles() {
    console.log('🗂️  删除未使用的组件文件...');
    
    const unusedComponentFiles = [
      'components/common/card/card.json',
      'components/common/loading/loading.json', 
      'components/list-item/list-item.json',
      'components/workspace/data-card/data-card.json',
      'components/workspace/loading-state/loading-state.json'
    ];

    for (const componentFile of unusedComponentFiles) {
      const fullPath = path.join(this.rootPath, componentFile);
      
      if (fs.existsSync(fullPath)) {
        // 检查组件是否真的未被使用
        const componentDir = path.dirname(componentFile);
        const isUsed = await this.checkComponentUsage(componentDir);
        
        if (!isUsed) {
          // 备份整个组件目录
          await this.backupComponentDirectory(componentDir);
          
          // 删除整个组件目录
          const componentDirPath = path.join(this.rootPath, componentDir);
          this.removeDirectory(componentDirPath);
          
          this.fixedIssues.push(`✅ 删除未使用组件: ${componentDir}`);
          this.sizeSavings += 5; // 每个组件约5KB
        }
      }
    }
  }

  /**
   * 检查组件是否被使用
   */
  async checkComponentUsage(componentDir) {
    const componentName = path.basename(componentDir);
    
    // 搜索所有JSON配置文件
    const jsonFiles = this.findJSONFiles();
    
    for (const jsonFile of jsonFiles) {
      try {
        const content = fs.readFileSync(jsonFile, 'utf8');
        const config = JSON.parse(content);
        
        if (config.usingComponents) {
          for (const [name, componentPath] of Object.entries(config.usingComponents)) {
            if (componentPath.includes(componentDir) || name.includes(componentName)) {
              return true;
            }
          }
        }
      } catch (error) {
        // 忽略解析错误
      }
    }
    
    return false;
  }

  /**
   * 优化主包大小
   */
  async optimizeMainPackageSize() {
    console.log('📦 优化主包大小...');
    
    // 启用按需注入
    await this.enableLazyCodeLoading();
    
    // 创建精简版常量文件
    await this.createLiteConstants();
    
    this.fixedIssues.push('✅ 启用组件按需注入');
    this.fixedIssues.push('✅ 创建精简版常量文件');
    this.sizeSavings += 30; // 预计减少30KB
  }

  /**
   * 启用按需注入
   */
  async enableLazyCodeLoading() {
    const appJsonPath = path.join(this.rootPath, 'app.json');
    
    if (fs.existsSync(appJsonPath)) {
      try {
        const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
        
        if (!appConfig.lazyCodeLoading) {
          appConfig.lazyCodeLoading = "requiredComponents";
          fs.writeFileSync(appJsonPath, JSON.stringify(appConfig, null, 2));
        }
      } catch (error) {
        this.errors.push(`启用按需注入失败: ${error.message}`);
      }
    }
  }

  /**
   * 创建精简版常量文件
   */
  async createLiteConstants() {
    const liteConstantsPath = path.join(this.rootPath, 'constants/lite.constants.js');
    
    const liteContent = `// constants/lite.constants.js - 精简版常量文件
// 只包含启动和认证必需的常量

module.exports = {
  // 核心API端点
  ENDPOINTS: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    USER_INFO: '/api/user/info',
    REFRESH_TOKEN: '/api/auth/refresh'
  },
  
  // 用户相关常量
  USER: {
    ROLES: {
      ADMIN: 'admin',
      USER: 'user',
      GUEST: 'guest'
    },
    STATUS: {
      ACTIVE: 'active',
      INACTIVE: 'inactive'
    }
  },
  
  // UI常量
  UI: {
    COLORS: {
      PRIMARY: '#1890ff',
      SUCCESS: '#52c41a',
      WARNING: '#faad14',
      ERROR: '#f5222d'
    },
    SIZES: {
      SMALL: 'small',
      MEDIUM: 'medium', 
      LARGE: 'large'
    }
  }
};`;

    try {
      fs.writeFileSync(liteConstantsPath, liteContent);
    } catch (error) {
      this.errors.push(`创建精简版常量文件失败: ${error.message}`);
    }
  }

  /**
   * 工具方法
   */
  findJSFiles() {
    const jsFiles = [];
    
    const searchDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          searchDir(fullPath);
        } else if (stat.isFile() && item.endsWith('.js')) {
          jsFiles.push(fullPath);
        }
      }
    };
    
    searchDir(this.rootPath);
    return jsFiles;
  }

  findJSONFiles() {
    const jsonFiles = [];
    
    const searchDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          searchDir(fullPath);
        } else if (stat.isFile() && item.endsWith('.json')) {
          jsonFiles.push(fullPath);
        }
      }
    };
    
    searchDir(this.rootPath);
    return jsonFiles;
  }

  async backupFile(filePath) {
    const backupDir = path.join(this.rootPath, 'backup/quality-fix');
    const backupPath = path.join(backupDir, filePath);
    
    // 创建备份目录
    fs.mkdirSync(path.dirname(backupPath), { recursive: true });
    
    // 复制文件
    fs.copyFileSync(path.join(this.rootPath, filePath), backupPath);
  }

  async backupComponentDirectory(componentDir) {
    const backupDir = path.join(this.rootPath, 'backup/quality-fix', componentDir);
    const sourceDir = path.join(this.rootPath, componentDir);
    
    // 创建备份目录
    fs.mkdirSync(backupDir, { recursive: true });
    
    // 复制目录内容
    const items = fs.readdirSync(sourceDir);
    for (const item of items) {
      const sourcePath = path.join(sourceDir, item);
      const backupPath = path.join(backupDir, item);
      fs.copyFileSync(sourcePath, backupPath);
    }
  }

  removeDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
    }
  }

  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return Math.round(stats.size / 1024); // KB
    } catch {
      return 0;
    }
  }

  /**
   * 生成修复报告
   */
  generateFixReport() {
    const reportContent = `# 代码质量问题修复报告

## 📊 修复概览
- 修复时间: ${new Date().toLocaleString()}
- 修复项目: ${this.fixedIssues.length}个
- 错误数量: ${this.errors.length}个
- 预计减少包体积: ${this.sizeSavings}KB

## ✅ 修复成功的项目
${this.fixedIssues.map(issue => `- ${issue}`).join('\n')}

## ❌ 修复失败的项目
${this.errors.length > 0 ? this.errors.map(error => `- ${error}`).join('\n') : '无'}

## 📈 优化效果
- 主包体积优化: 预计减少${this.sizeSavings}KB
- 组件按需加载: 已启用
- 未使用文件清理: 已完成
- 组件声明优化: 已完成

## 🔄 后续建议
1. 运行小程序开发者工具检查包体积
2. 测试所有页面功能是否正常
3. 检查是否有遗漏的依赖关系
4. 考虑进一步的代码分割优化
`;

    const reportPath = path.join(this.rootPath, 'docs/quality-fix-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log('\n📋 修复报告已生成:', reportPath);
    console.log(`\n🎉 修复完成！共修复 ${this.fixedIssues.length} 个问题，预计减少包体积 ${this.sizeSavings}KB`);
  }
}

// 执行修复
if (require.main === module) {
  const fixer = new QualityIssueFixer();
  fixer.fixAllIssues().catch(console.error);
}

module.exports = QualityIssueFixer;
