#!/usr/bin/env node

/**
 * 强制清理未使用文件 - 解决微信小程序代码质量审查问题
 * 基于微信开发者工具的实际检测结果
 */

const fs = require('fs');
const path = require('path');

class ForceCleanupTool {
  constructor() {
    this.rootPath = process.cwd();
    this.backupDir = path.join(this.rootPath, 'cleanup_backup', new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
    this.removedFiles = [];
    this.errors = [];
  }

  /**
   * 执行强制清理
   */
  async forceCleanup() {
    console.log('🚨 开始强制清理未使用文件（基于微信开发者工具检测）...\n');

    // 创建备份目录
    this.ensureBackupDir();

    // 1. 清理未使用的JS文件
    await this.removeUnusedJSFiles();

    // 2. 清理未使用的组件
    await this.removeUnusedComponents();

    // 3. 生成清理报告
    this.generateCleanupReport();

    console.log('\n✅ 强制清理完成！现在应该可以通过微信小程序代码质量审查了。');
  }

  /**
   * 移除未使用的JS文件
   */
  async removeUnusedJSFiles() {
    console.log('🗑️  移除未使用的JS文件...');

    const unusedJSFiles = [
      'utils/business/batch-management.js',
      'utils/business/finance-mock-data.js',
      'utils/business/finance-service.js',
      'utils/business/health-finance-integration.js',
      'utils/helpers/auth-helper.js',
      'utils/helpers/permission-diagnostic.js',
      'utils/payment/index.js'
    ];

    for (const filePath of unusedJSFiles) {
      await this.removeFileWithBackup(filePath, '微信开发者工具检测为未使用');
    }
  }

  /**
   * 移除未使用的组件
   */
  async removeUnusedComponents() {
    console.log('🧩 移除未使用的组件...');

    // 移除整个loading-state组件目录
    const componentDir = 'components/workspace/loading-state';
    await this.removeDirectoryWithBackup(componentDir, '组件未被使用');
  }

  /**
   * 备份并删除文件
   */
  async removeFileWithBackup(filePath, reason) {
    const fullPath = path.join(this.rootPath, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`   ⚠️  文件不存在，跳过: ${filePath}`);
      return;
    }

    try {
      // 备份文件
      const backupPath = path.join(this.backupDir, filePath);
      const backupDirPath = path.dirname(backupPath);
      
      if (!fs.existsSync(backupDirPath)) {
        fs.mkdirSync(backupDirPath, { recursive: true });
      }
      
      fs.copyFileSync(fullPath, backupPath);
      
      // 删除原文件
      fs.unlinkSync(fullPath);
      
      const fileSize = fs.statSync(backupPath).size;
      
      this.removedFiles.push({
        file: filePath,
        reason: reason,
        size: fileSize,
        backupPath: backupPath
      });
      
      console.log(`   ✅ 已删除: ${filePath} (${this.formatSize(fileSize)})`);
      
    } catch (error) {
      console.error(`   ❌ 删除失败: ${filePath} - ${error.message}`);
      this.errors.push(`删除 ${filePath} 失败: ${error.message}`);
    }
  }

  /**
   * 备份并删除目录
   */
  async removeDirectoryWithBackup(dirPath, reason) {
    const fullPath = path.join(this.rootPath, dirPath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`   ⚠️  目录不存在，跳过: ${dirPath}`);
      return;
    }

    try {
      // 备份整个目录
      const backupPath = path.join(this.backupDir, dirPath);
      this.copyDirectory(fullPath, backupPath);
      
      // 删除原目录
      this.removeDirectory(fullPath);
      
      const dirSize = this.calculateDirectorySize(backupPath);
      
      this.removedFiles.push({
        file: dirPath,
        reason: reason,
        size: dirSize,
        backupPath: backupPath,
        type: 'directory'
      });
      
      console.log(`   ✅ 已删除目录: ${dirPath} (${this.formatSize(dirSize)})`);
      
    } catch (error) {
      console.error(`   ❌ 删除目录失败: ${dirPath} - ${error.message}`);
      this.errors.push(`删除目录 ${dirPath} 失败: ${error.message}`);
    }
  }

  /**
   * 工具方法
   */
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log(`📁 创建备份目录: ${this.backupDir}`);
    }
  }

  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const items = fs.readdirSync(src);
    items.forEach(item => {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  removeDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
      const items = fs.readdirSync(dirPath);
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        if (fs.statSync(itemPath).isDirectory()) {
          this.removeDirectory(itemPath);
        } else {
          fs.unlinkSync(itemPath);
        }
      });
      fs.rmdirSync(dirPath);
    }
  }

  calculateDirectorySize(dirPath) {
    let totalSize = 0;
    
    if (!fs.existsSync(dirPath)) return 0;
    
    const items = fs.readdirSync(dirPath);
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        totalSize += this.calculateDirectorySize(itemPath);
      } else {
        totalSize += stat.size;
      }
    });
    
    return totalSize;
  }

  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成清理报告
   */
  generateCleanupReport() {
    console.log('\n📋 强制清理报告');
    console.log('=' .repeat(50));

    if (this.removedFiles.length > 0) {
      console.log('\n✅ 已删除的文件:');
      let totalSize = 0;
      
      this.removedFiles.forEach(item => {
        const type = item.type === 'directory' ? '目录' : '文件';
        console.log(`   ${type}: ${item.file}`);
        console.log(`      原因: ${item.reason}`);
        console.log(`      大小: ${this.formatSize(item.size)}`);
        console.log(`      备份: ${item.backupPath}`);
        console.log('');
        totalSize += item.size;
      });
      
      console.log(`📊 总计删除: ${this.removedFiles.length} 个项目`);
      console.log(`💾 节省空间: ${this.formatSize(totalSize)}`);
    }

    if (this.errors.length > 0) {
      console.log('\n❌ 错误信息:');
      this.errors.forEach(error => {
        console.log(`   ${error}`);
      });
    }

    console.log('\n📁 备份位置:');
    console.log(`   ${this.backupDir}`);
    
    console.log('\n💡 下一步:');
    console.log('   1. 使用微信开发者工具重新检查代码质量');
    console.log('   2. 如果通过审查，可以删除备份文件');
    console.log('   3. 如果出现问题，可以从备份恢复文件');
  }
}

// 执行强制清理
if (require.main === module) {
  const cleaner = new ForceCleanupTool();
  cleaner.forceCleanup().catch(console.error);
}

module.exports = ForceCleanupTool;
