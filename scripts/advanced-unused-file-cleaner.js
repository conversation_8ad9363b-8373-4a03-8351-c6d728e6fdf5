#!/usr/bin/env node

// scripts/advanced-unused-file-cleaner.js - 高级未使用文件清理工具
const fs = require('fs');
const path = require('path');

class AdvancedUnusedFileCleaner {
  constructor() {
    this.rootPath = process.cwd();
    this.cleanedFiles = [];
    this.errors = [];
    this.sizeSavings = 0;
    this.backupDir = path.join(this.rootPath, 'backup/unused-files-cleanup');
  }

  /**
   * 执行高级清理
   */
  async performAdvancedCleanup() {
    console.log('🔍 开始高级未使用文件检测和清理...\n');
    
    try {
      // 1. 检查并清理真正未使用的JS文件
      await this.cleanUnusedJSFiles();
      
      // 2. 清理空目录
      await this.cleanEmptyDirectories();
      
      // 3. 检查主包大小问题
      await this.analyzeMainPackageSize();
      
      // 4. 生成详细报告
      this.generateDetailedReport();
      
    } catch (error) {
      console.error('清理过程中出现错误:', error);
      this.errors.push(`清理失败: ${error.message}`);
    }
  }

  /**
   * 清理真正未使用的JS文件
   */
  async cleanUnusedJSFiles() {
    console.log('🗑️  检测并清理真正未使用的JS文件...');
    
    // 需要检查的可疑文件列表
    const suspiciousFiles = [
      'utils/business/patch-management.js',
      'utils/helpers/auth-helper.js',
      'utils/payment/index.js'
    ];

    for (const filePath of suspiciousFiles) {
      const fullPath = path.join(this.rootPath, filePath);
      
      // 检查文件是否存在
      if (!fs.existsSync(fullPath)) {
        console.log(`⚠️  文件不存在，跳过: ${filePath}`);
        continue;
      }

      // 进行深度使用检查
      const usageInfo = await this.deepCheckFileUsage(filePath);
      
      if (!usageInfo.isUsed) {
        console.log(`🗑️  准备删除未使用文件: ${filePath}`);
        
        // 备份文件
        await this.backupFile(filePath);
        
        // 删除文件
        fs.unlinkSync(fullPath);
        
        this.cleanedFiles.push({
          file: filePath,
          reason: '文件未被任何地方引用',
          size: this.getFileSize(fullPath),
          references: usageInfo.references
        });
        
        this.sizeSavings += this.getFileSize(fullPath);
      } else {
        console.log(`✅ 文件仍在使用: ${filePath}`);
        console.log(`   引用位置: ${usageInfo.references.join(', ')}`);
      }
    }
  }

  /**
   * 深度检查文件使用情况
   */
  async deepCheckFileUsage(filePath) {
    const fileName = path.basename(filePath, '.js');
    const relativePath = filePath.replace(/\\/g, '/');
    const references = [];
    
    // 更精确的搜索模式
    const searchPatterns = [
      // 直接require路径
      `require('${relativePath}')`,
      `require("./${relativePath}")`,
      `require("../${relativePath}")`,
      `require("../../${relativePath}")`,
      `require("../../../${relativePath}")`,
      `require("../../../../${relativePath}")`,
      
      // 相对路径变体
      `require('./${relativePath}')`,
      `require('../${relativePath}')`,
      `require('../../${relativePath}')`,
      `require('../../../${relativePath}')`,
      `require('../../../../${relativePath}')`,
      
      // ES6 import
      `from '${relativePath}'`,
      `from "./${relativePath}"`,
      `from "../${relativePath}"`,
      
      // 文件名匹配
      fileName,
      
      // 类名或对象名匹配（更宽松）
      fileName.charAt(0).toUpperCase() + fileName.slice(1)
    ];

    // 搜索所有相关文件
    const filesToSearch = [
      ...this.findJSFiles(),
      ...this.findJSONFiles(),
      ...this.findWXMLFiles()
    ];
    
    for (const searchFile of filesToSearch) {
      // 跳过自身
      if (searchFile === path.join(this.rootPath, filePath)) continue;
      
      try {
        const content = fs.readFileSync(searchFile, 'utf8');
        
        for (const pattern of searchPatterns) {
          if (content.includes(pattern)) {
            references.push(path.relative(this.rootPath, searchFile));
          }
        }
      } catch (error) {
        // 忽略读取错误
      }
    }
    
    return {
      isUsed: references.length > 0,
      references: references
    };
  }

  /**
   * 清理空目录
   */
  async cleanEmptyDirectories() {
    console.log('📁 清理空目录...');
    
    const emptyDirs = this.findEmptyDirectories();
    
    for (const emptyDir of emptyDirs) {
      try {
        fs.rmdirSync(emptyDir);
        this.cleanedFiles.push({
          file: emptyDir,
          reason: '空目录',
          size: 0
        });
        console.log(`✅ 删除空目录: ${path.relative(this.rootPath, emptyDir)}`);
      } catch (error) {
        console.warn(`⚠️  删除目录失败: ${emptyDir} - ${error.message}`);
      }
    }
  }

  /**
   * 分析主包大小问题
   */
  async analyzeMainPackageSize() {
    console.log('📦 分析主包大小问题...');
    
    const largeFiles = this.findLargeFiles();
    const mainPackageFiles = this.getMainPackageFiles();
    
    console.log('\n📊 主包大文件分析:');
    largeFiles.forEach(file => {
      const isInMainPackage = mainPackageFiles.includes(file.path);
      const status = isInMainPackage ? '🔴 主包' : '🟢 分包';
      console.log(`${status} ${file.path} (${file.size}KB)`);
    });
    
    // 计算主包总大小
    let mainPackageSize = 0;
    mainPackageFiles.forEach(filePath => {
      const fullPath = path.join(this.rootPath, filePath);
      if (fs.existsSync(fullPath)) {
        mainPackageSize += this.getFileSize(fullPath);
      }
    });
    
    console.log(`\n📏 主包总大小: ${mainPackageSize}KB`);
    
    if (mainPackageSize > 1500) {
      console.log('⚠️  主包大小超过1.5MB，建议进一步优化');
      this.suggestMainPackageOptimizations(largeFiles, mainPackageFiles);
    }
  }

  /**
   * 建议主包优化方案
   */
  suggestMainPackageOptimizations(largeFiles, mainPackageFiles) {
    console.log('\n💡 主包优化建议:');
    
    const mainPackageLargeFiles = largeFiles.filter(file => 
      mainPackageFiles.includes(file.path) && file.size > 30
    );
    
    mainPackageLargeFiles.forEach(file => {
      console.log(`📄 ${file.path} (${file.size}KB)`);
      
      if (file.path.includes('pages/')) {
        console.log(`   建议: 考虑将页面移至分包`);
      } else if (file.path.includes('utils/')) {
        console.log(`   建议: 考虑按需加载或拆分工具函数`);
      } else if (file.path.includes('constants/')) {
        console.log(`   建议: 创建精简版常量文件`);
      } else if (file.path.includes('styles/')) {
        console.log(`   建议: 按模块拆分样式文件`);
      }
    });
  }

  /**
   * 工具方法
   */
  findJSFiles() {
    return this.findFilesByExtension('.js');
  }

  findJSONFiles() {
    return this.findFilesByExtension('.json');
  }

  findWXMLFiles() {
    return this.findFilesByExtension('.wxml');
  }

  findFilesByExtension(extension) {
    const files = [];
    
    const searchDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && 
              item !== 'node_modules' && item !== 'backup') {
            searchDir(fullPath);
          } else if (stat.isFile() && item.endsWith(extension)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // 忽略访问错误
      }
    };
    
    searchDir(this.rootPath);
    return files;
  }

  findEmptyDirectories() {
    const emptyDirs = [];
    
    const checkDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        if (items.length === 0) {
          emptyDirs.push(dir);
          return;
        }
        
        let hasFiles = false;
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isFile()) {
            hasFiles = true;
            break;
          } else if (stat.isDirectory()) {
            checkDir(fullPath);
          }
        }
        
        // 重新检查是否为空（子目录可能被删除）
        const currentItems = fs.readdirSync(dir);
        if (currentItems.length === 0) {
          emptyDirs.push(dir);
        }
      } catch (error) {
        // 忽略访问错误
      }
    };
    
    // 只检查特定目录
    const dirsToCheck = [
      path.join(this.rootPath, 'components'),
      path.join(this.rootPath, 'utils'),
      path.join(this.rootPath, 'pages')
    ];
    
    dirsToCheck.forEach(dir => {
      if (fs.existsSync(dir)) {
        checkDir(dir);
      }
    });
    
    return emptyDirs;
  }

  findLargeFiles(minSizeKB = 30) {
    const largeFiles = [];
    
    const checkFile = (filePath) => {
      try {
        const stat = fs.statSync(filePath);
        const sizeKB = Math.round(stat.size / 1024);
        
        if (sizeKB >= minSizeKB) {
          largeFiles.push({
            path: path.relative(this.rootPath, filePath),
            size: sizeKB
          });
        }
      } catch (error) {
        // 忽略错误
      }
    };
    
    // 检查所有JS、WXSS、JSON文件
    [...this.findJSFiles(), ...this.findFilesByExtension('.wxss'), ...this.findJSONFiles()]
      .forEach(checkFile);
    
    return largeFiles.sort((a, b) => b.size - a.size);
  }

  getMainPackageFiles() {
    // 主包文件通常包括：
    // 1. app.js, app.json, app.wxss
    // 2. 主包页面（非分包页面）
    // 3. 全局工具和常量
    
    const mainPackageFiles = [
      'app.js',
      'app.json', 
      'app.wxss'
    ];
    
    // 添加主包页面（需要根据app.json配置判断）
    try {
      const appJsonPath = path.join(this.rootPath, 'app.json');
      if (fs.existsSync(appJsonPath)) {
        const appConfig = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
        
        // 添加主包页面
        if (appConfig.pages) {
          appConfig.pages.forEach(page => {
            mainPackageFiles.push(`${page}.js`);
            mainPackageFiles.push(`${page}.json`);
            mainPackageFiles.push(`${page}.wxml`);
            mainPackageFiles.push(`${page}.wxss`);
          });
        }
      }
    } catch (error) {
      console.warn('读取app.json失败:', error.message);
    }
    
    // 添加常用的全局文件
    const globalFiles = [
      'utils/request.js',
      'utils/logger.js',
      'constants/index.js',
      'constants/api.constants.js',
      'constants/business.constants.js'
    ];
    
    globalFiles.forEach(file => {
      if (fs.existsSync(path.join(this.rootPath, file))) {
        mainPackageFiles.push(file);
      }
    });
    
    return mainPackageFiles;
  }

  async backupFile(filePath) {
    const backupPath = path.join(this.backupDir, filePath);
    
    // 创建备份目录
    fs.mkdirSync(path.dirname(backupPath), { recursive: true });
    
    // 复制文件
    fs.copyFileSync(path.join(this.rootPath, filePath), backupPath);
  }

  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return Math.round(stats.size / 1024); // KB
    } catch {
      return 0;
    }
  }

  /**
   * 生成详细报告
   */
  generateDetailedReport() {
    const reportContent = `# 高级未使用文件清理报告

## 📊 清理概览
- 清理时间: ${new Date().toLocaleString()}
- 清理文件: ${this.cleanedFiles.length}个
- 错误数量: ${this.errors.length}个
- 节省空间: ${this.sizeSavings}KB

## 🗑️ 已清理的文件
${this.cleanedFiles.map(item => `- ${item.file} (${item.size}KB) - ${item.reason}`).join('\n')}

## ❌ 清理失败的项目
${this.errors.length > 0 ? this.errors.map(error => `- ${error}`).join('\n') : '无'}

## 📈 优化效果
- 删除未使用文件: ${this.cleanedFiles.filter(f => f.reason.includes('未被引用')).length}个
- 清理空目录: ${this.cleanedFiles.filter(f => f.reason === '空目录').length}个
- 总计节省空间: ${this.sizeSavings}KB

## 🔄 后续建议
1. 运行小程序开发者工具检查功能是否正常
2. 检查主包大小是否符合要求
3. 考虑进一步的代码分割和优化
4. 定期运行此清理工具维护代码库
`;

    const reportPath = path.join(this.rootPath, 'docs/advanced-cleanup-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log('\n📋 详细清理报告已生成:', reportPath);
    console.log(`\n🎉 高级清理完成！共清理 ${this.cleanedFiles.length} 个项目，节省空间 ${this.sizeSavings}KB`);
  }
}

// 执行清理
if (require.main === module) {
  const cleaner = new AdvancedUnusedFileCleaner();
  cleaner.performAdvancedCleanup().catch(console.error);
}

module.exports = AdvancedUnusedFileCleaner;
